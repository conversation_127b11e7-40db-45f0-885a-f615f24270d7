{"name": "tehseen.io", "version": "0.1.0", "private": true, "scripts": {"clean": "git clean -xdf node_modules .next .velite", "dev": "next dev | pino-pretty", "build": "next build", "start": "next start", "lint": "biome lint", "velite": "velite", "db:push": "drizzle-kit push --config=drizzle.config.ts", "db:migrate": "drizzle-kit generate --config=drizzle.config.ts", "db:studio": "drizzle-kit studio", "format": "biome format --write . --log-level warn", "typecheck": "tsc --noEmit", "lint-staged": "lint-staged", "prepare": "husky"}, "dependencies": {"@auth/drizzle-adapter": "^1.9.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.0.41", "@t3-oss/env-nextjs": "^0.13.4", "@tailwindcss/postcss": "^4.1.7", "@vercel/analytics": "^1.5.0", "@vercel/postgres": "^0.10.0", "better-auth": "^1.2.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-orm": "^0.43.1", "geist": "^1.4.2", "lucide-react": "^0.511.0", "motion": "^12.12.2", "next": "15.3.2", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "nodemailer": "^7.0.3", "pino": "^9.7.0", "postcss": "^8.5.3", "react": "^19", "react-dom": "^19", "react-email": "^4.0.15", "rehype-autolink-headings": "^7.1.0", "rehype-pretty-code": "^0.14.1", "rehype-slug": "^6.0.0", "sonner": "^2.0.3", "sugar-high": "^0.9.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "tailwindcss-animate": "^1.0.7", "tiny-invariant": "^1.3.3", "zod": "^3.25.28"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@next/env": "^15.3.2", "@types/node": "^22", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.1", "husky": "^9.1.7", "lint-staged": "^16.0.0", "pg": "^8.16.0", "pino-pretty": "^13.0.0", "shiki": "^3.4.2", "tsc-files": "^1.1.4", "typescript": "^5.8.3", "velite": "^0.2.4"}, "license": "MIT", "author": "<PERSON> <contact.te<PERSON><PERSON><PERSON><PERSON>@gmail.com>"}