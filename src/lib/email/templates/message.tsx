import type React from 'react';

import {
  EmailContainer,
  EmailLink,
  EmailParagraph,
  EmailStructure,
} from '@/lib/email/_components/email-components';
import { EmailFooter } from '@/lib/email/_components/email-footer';
import { EmailHeader } from '@/lib/email/_components/email-header';
import { render } from '@react-email/components';

export interface MessageEmailProps {
  senderName: string;
  senderEmail: string;
  message: string;
  receiverName: string;
  receiverEmail: string;
}

export function MessageEmail({
  senderName = '[SENDER_NAME]',
  senderEmail = '[SENDER_EMAIL]',
  message = '[MESSAGE]',
  receiverName = '[RECEIVER_NAME]',
  receiverEmail = '[RECEIVER_EMAIL]',
}: MessageEmailProps) {
  const previewText = `New message from ${senderName}`;

  return (
    <EmailStructure previewText={previewText}>
      <EmailContainer>
        <EmailHeader />

        <EmailParagraph>Hi {receiverName},</EmailParagraph>

        <EmailParagraph>
          You have received a new message from {senderName} (
          <EmailLink href={`mailto:${senderEmail}`}>{senderEmail}</EmailLink>
          ).
        </EmailParagraph>

        <EmailParagraph>{message}</EmailParagraph>

        <EmailParagraph>
          You can reply directly to this email to respond to {senderName}.
        </EmailParagraph>

        <EmailFooter receiverEmail={receiverEmail} />
      </EmailContainer>
    </EmailStructure>
  );
}

export function renderMessageEmail(props: MessageEmailProps) {
  return render(<MessageEmail {...props} />);
}
