import { render } from '@react-email/render';

import { EmailProvider } from './index';
import { MessageEmail, type MessageEmailProps } from './templates/message';

export async function sendMessageEmail(
  input: MessageEmailProps & { recipient: string }
): Promise<void> {
  const component = MessageEmail(input);
  const html = await render(component);
  const text = await render(component, { plainText: true });

  await EmailProvider.sendEmail({
    recipient: input.recipient,
    subject: 'New Message',
    html,
    text,
  });
}
