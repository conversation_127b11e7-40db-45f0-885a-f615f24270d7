import type { Metadata } from 'next';

import { GeistSans } from 'geist/font/sans';

import { Footer } from '@/components/footer';
import { Header } from '@/components/header';
import { Providers } from '@/components/providers';
import { Analytics } from '@vercel/analytics/next';

import { cn } from '@/utils';

import './globals.css';

import { createMetadata } from '@/utils/seo';

export const metadata: Metadata = createMetadata({});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={cn(GeistSans.variable)}>
      <body className="relative">
        <Providers>
          <main className="mx-auto max-w-2xl px-6 antialiased mt-6 sm:mt-16 mb-24 space-y-10">
            <Header />
            <div>{children}</div>
            <Footer />
          </main>

          {/* <div className="w-full fixed bottom-4">
            <FloatingNavbar />
          </div> */}
        </Providers>
        <Analytics />
      </body>
    </html>
  );
}
